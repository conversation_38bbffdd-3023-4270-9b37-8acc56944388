import type { CryptoCurrencyStatisticsDto } from '../generated';
import type { SortConfig, SortColumn, SortDirection } from '../types/table';

const SIGNAL_PRIORITY = {
  gold: 3,
  blue: 2,
  gray: 1,
  null: 0,
  undefined: 0,
} as const;

export const getSignalPriority = (color: string | null | undefined): number => {
  if (!color) return SIGNAL_PRIORITY.null;
  return SIGNAL_PRIORITY[color as keyof typeof SIGNAL_PRIORITY] || SIGNAL_PRIORITY.null;
};

export const getSortValue = (
  crypto: CryptoCurrencyStatisticsDto,
  btcData: any,
  column: SortColumn
): any => {
  const usdData = crypto.indicatorValues?.[0];

  switch (column) {
    case 'symbol':
      return crypto.symbol || '';
    case 'usdPrice':
      return usdData?.close || 0;
    case 'marketCap':
      return usdData?.marketCap || 0;
    case 'usdSignal':
      return getSignalPriority(usdData?.color);
    case 'btcPrice':
      return btcData?.close || 0;
    case 'btcSignal':
      return getSignalPriority(btcData?.color);
    default:
      return '';
  }
};

export const compareValues = (a: any, b: any, direction: SortDirection): number => {
  if (direction === null) return 0;
  
  // Handle null/undefined values
  if (a == null && b == null) return 0;
  if (a == null) return direction === 'asc' ? 1 : -1;
  if (b == null) return direction === 'asc' ? -1 : 1;

  // Compare values
  let result = 0;
  if (typeof a === 'string' && typeof b === 'string') {
    result = a.localeCompare(b);
  } else {
    result = a - b;
  }

  return direction === 'asc' ? result : -result;
};

export const applySorting = (
  data: CryptoCurrencyStatisticsDto[],
  btcStatistics: CryptoCurrencyStatisticsDto[],
  sortConfig: SortConfig,
  findBtcDataForSymbol: (btcStats: CryptoCurrencyStatisticsDto[], symbol: string) => any
): CryptoCurrencyStatisticsDto[] => {
  if (!sortConfig.column || !sortConfig.direction) {
    return data;
  }

  return [...data].sort((a, b) => {
    const btcDataA = findBtcDataForSymbol(btcStatistics, a.symbol);
    const btcDataB = findBtcDataForSymbol(btcStatistics, b.symbol);
    
    const valueA = getSortValue(a, btcDataA, sortConfig.column!);
    const valueB = getSortValue(b, btcDataB, sortConfig.column!);
    
    const primaryResult = compareValues(valueA, valueB, sortConfig.direction);
    
    // Secondary sort by symbol for stable sorting
    if (primaryResult === 0) {
      return a.symbol.localeCompare(b.symbol);
    }
    
    return primaryResult;
  });
};
