import React from 'react';
import type { SortColumn, SortDirection } from '../../types/table';
import { TABLE_HEADERS } from '../../constants/app';

interface SortableTableHeaderProps {
  onSort: (column: SortColumn) => void;
  getSortDirection: (column: SortColumn) => SortDirection;
}

const getSortIcon = (direction: SortDirection): string => {
  switch (direction) {
    case 'asc':
      return '▲';
    case 'desc':
      return '▼';
    default:
      return '↕';
  }
};

const COLUMN_MAPPING: Record<string, SortColumn> = {
  [TABLE_HEADERS.CRYPTOCURRENCY]: 'symbol',
  [TABLE_HEADERS.USD_PRICE]: 'usdPrice',
  [TABLE_HEADERS.MARKET_CAP]: 'marketCap',
  [TABLE_HEADERS.USD_SIGNAL]: 'usdSignal',
  [TABLE_HEADERS.BTC_PRICE]: 'btcPrice',
  [TABLE_HEADERS.BTC_SIGNAL]: 'btcSignal',
};

export const SortableTableHeader: React.FC<SortableTableHeaderProps> = ({
  onSort,
  getSortDirection,
}) => {
  const handleHeaderClick = (headerText: string) => {
    const column = COLUMN_MAPPING[headerText];
    if (column) {
      onSort(column);
    }
  };

  const renderSortableHeader = (headerText: string) => {
    const column = COLUMN_MAPPING[headerText];
    const direction = column ? getSortDirection(column) : null;
    const isActive = direction !== null;

    return (
      <th
        key={headerText}
        className={`sortable-header ${isActive ? 'active' : ''}`}
        onClick={() => handleHeaderClick(headerText)}
        tabIndex={0}
        onKeyDown={(e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            handleHeaderClick(headerText);
          }
        }}
        aria-sort={
          direction === 'asc' ? 'ascending' :
          direction === 'desc' ? 'descending' :
          'none'
        }
      >
        <div className="header-content">
          <span className="header-text">{headerText}</span>
          <span className="sort-icon">{getSortIcon(direction)}</span>
        </div>
      </th>
    );
  };

  return (
    <thead>
      <tr>
        {renderSortableHeader(TABLE_HEADERS.CRYPTOCURRENCY)}
        {renderSortableHeader(TABLE_HEADERS.USD_PRICE)}
        {renderSortableHeader(TABLE_HEADERS.MARKET_CAP)}
        {renderSortableHeader(TABLE_HEADERS.USD_SIGNAL)}
        {renderSortableHeader(TABLE_HEADERS.BTC_PRICE)}
        {renderSortableHeader(TABLE_HEADERS.BTC_SIGNAL)}
      </tr>
    </thead>
  );
};
